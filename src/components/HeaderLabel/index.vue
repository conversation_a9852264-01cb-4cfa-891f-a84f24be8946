<script lang="ts" setup>
defineOptions({
  name: 'HeaderLabel',
})

const props = withDefaults(defineProps<Props>(), {
  title: '',
})

interface Props {
  /* 标题 */
  title?: string
}
</script>

<template>
  <view class="header-label" flex flex-col items-start gap-16rpx bg-white py-12rpx border-l="2px solid #0085D0">
    <text text-36rpx>{{ props.title }}</text>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
